// Contact Page JavaScript

// EmailJS Configuration
// Replace these with your actual EmailJS credentials
const EMAILJS_CONFIG = {
    serviceID: 'service_mv8fvtq',
    templateID: 'template_q6xx9wj',
    publicKey: 'w7dn0q-39vtscCXW1'
};

document.addEventListener('DOMContentLoaded', function() {
    // Initialize EmailJS
    emailjs.init(EMAILJS_CONFIG.publicKey);

    // Initialize animations
    initializeAnimations();

    // Initialize form functionality
    initializeContactForm();

    // Initialize scroll effects
    initializeScrollEffects();

    // Initialize hover effects
    initializeHoverEffects();
});

function initializeAnimations() {
    // Animate hero section on load
    gsap.from('.contact-hero h1', {
        duration: 1,
        y: 50,
        opacity: 0,
        ease: 'power3.out'
    });
    
    gsap.from('.contact-hero p', {
        duration: 1,
        y: 30,
        opacity: 0,
        delay: 0.2,
        ease: 'power3.out'
    });
    
    // Animate contact sections
    gsap.from('.contact-form-section', {
        duration: 1,
        x: -50,
        opacity: 0,
        delay: 0.5,
        ease: 'power3.out'
    });
    
    gsap.from('.contact-info-section', {
        duration: 1,
        x: 50,
        opacity: 0,
        delay: 0.7,
        ease: 'power3.out'
    });
}

function initializeContactForm() {
    const form = document.getElementById('contactForm');
    const submitBtn = document.querySelector('.submit-btn');
    
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Validate form
            if (validateForm()) {
                // Animate submit button
                animateSubmitButton();

                // Send email using EmailJS
                sendEmail(form);
            }
        });
        
        // Add real-time validation
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                clearFieldError(this);
            });
        });
    }
}

function sendEmail(form) {
    // Prepare template parameters
    const templateParams = {
        from_name: form.name.value,
        from_email: form.email.value,
        subject: form.subject.value,
        project_type: form['project-type'].value,
        budget: form.budget.value,
        message: form.message.value,
        to_email: '<EMAIL>'
    };

    // Send email
    emailjs.send(EMAILJS_CONFIG.serviceID, EMAILJS_CONFIG.templateID, templateParams)
        .then(function(response) {
            console.log('SUCCESS!', response.status, response.text);
            showSuccessMessage();
            resetForm();
        })
        .catch(function(error) {
            console.log('FAILED...', error);
            showErrorMessage();
        })
        .finally(function() {
            // Reset submit button
            setTimeout(() => {
                const submitBtn = document.querySelector('.submit-btn');
                submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Send Message';
                submitBtn.disabled = false;
                gsap.to(submitBtn, {
                    duration: 0.3,
                    scale: 1,
                    ease: 'power2.out'
                });
            }, 1000);
        });
}

function validateForm() {
    const form = document.getElementById('contactForm');
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!validateField(field)) {
            isValid = false;
        }
    });
    
    return isValid;
}

function validateField(field) {
    const value = field.value.trim();
    const fieldGroup = field.closest('.form-group');
    
    // Remove existing error
    clearFieldError(field);
    
    // Check if required field is empty
    if (field.hasAttribute('required') && !value) {
        showFieldError(field, 'This field is required');
        return false;
    }
    
    // Email validation
    if (field.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            showFieldError(field, 'Please enter a valid email address');
            return false;
        }
    }
    
    return true;
}

function showFieldError(field, message) {
    const fieldGroup = field.closest('.form-group');
    const errorElement = document.createElement('span');
    errorElement.className = 'field-error';
    errorElement.textContent = message;
    errorElement.style.color = '#dc3545';
    errorElement.style.fontSize = '0.8rem';
    errorElement.style.marginTop = '0.25rem';
    errorElement.style.display = 'block';
    
    fieldGroup.appendChild(errorElement);
    field.style.borderColor = '#dc3545';
    
    // Animate error message
    gsap.from(errorElement, {
        duration: 0.3,
        opacity: 0,
        y: -10,
        ease: 'power2.out'
    });
}

function clearFieldError(field) {
    const fieldGroup = field.closest('.form-group');
    const errorElement = fieldGroup.querySelector('.field-error');
    
    if (errorElement) {
        errorElement.remove();
    }
    
    field.style.borderColor = '#e9ecef';
}

function animateSubmitButton() {
    const submitBtn = document.querySelector('.submit-btn');
    const originalText = submitBtn.innerHTML;
    
    // Change button text and disable
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    submitBtn.disabled = true;
    
    // Animate button
    gsap.to(submitBtn, {
        duration: 0.3,
        scale: 0.95,
        ease: 'power2.out'
    });
    
    // Reset after delay
    setTimeout(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        gsap.to(submitBtn, {
            duration: 0.3,
            scale: 1,
            ease: 'power2.out'
        });
    }, 2000);
}

function showSuccessMessage() {
    const form = document.getElementById('contactForm');
    const successMessage = document.createElement('div');
    successMessage.className = 'success-message';
    successMessage.innerHTML = `
        <div style="background: #d4edda; color: #155724; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border: 1px solid #c3e6cb;">
            <i class="fas fa-check-circle"></i> Thank you! Your message has been sent successfully. I'll get back to you within 24 hours.
        </div>
    `;
    
    form.parentNode.insertBefore(successMessage, form);
    
    // Animate success message
    gsap.from(successMessage, {
        duration: 0.5,
        opacity: 0,
        y: -20,
        ease: 'power2.out'
    });
    
    // Remove success message after 5 seconds
    setTimeout(() => {
        gsap.to(successMessage, {
            duration: 0.3,
            opacity: 0,
            y: -20,
            ease: 'power2.out',
            onComplete: () => successMessage.remove()
        });
    }, 5000);
}

function showErrorMessage() {
    const form = document.getElementById('contactForm');
    const errorMessage = document.createElement('div');
    errorMessage.className = 'error-message';
    errorMessage.innerHTML = `
        <div style="background: #f8d7da; color: #721c24; padding: 1rem; border-radius: 8px; margin-bottom: 1rem; border: 1px solid #f5c6cb;">
            <i class="fas fa-exclamation-triangle"></i> Sorry, there was an error sending your message. Please try again or contact me <NAME_EMAIL>
        </div>
    `;

    form.parentNode.insertBefore(errorMessage, form);

    // Animate error message
    gsap.from(errorMessage, {
        duration: 0.5,
        opacity: 0,
        y: -20,
        ease: 'power2.out'
    });

    // Remove error message after 7 seconds
    setTimeout(() => {
        gsap.to(errorMessage, {
            duration: 0.3,
            opacity: 0,
            y: -20,
            ease: 'power2.out',
            onComplete: () => errorMessage.remove()
        });
    }, 7000);
}

function resetForm() {
    const form = document.getElementById('contactForm');
    form.reset();
    
    // Clear any remaining errors
    const errorElements = form.querySelectorAll('.field-error');
    errorElements.forEach(error => error.remove());
    
    const inputs = form.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.style.borderColor = '#e9ecef';
    });
}

function initializeScrollEffects() {
    // Register ScrollTrigger plugin
    gsap.registerPlugin(ScrollTrigger);
    
    // Animate contact methods
    gsap.from('.contact-method', {
        duration: 0.8,
        x: -30,
        opacity: 0,
        stagger: 0.2,
        scrollTrigger: {
            trigger: '.contact-methods',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
        }
    });
    
    // Animate social links
    gsap.from('.social-icons a', {
        duration: 0.6,
        scale: 0,
        stagger: 0.1,
        scrollTrigger: {
            trigger: '.social-links',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
        },
        ease: 'back.out(1.7)'
    });
    
    // Animate FAQ items
    gsap.from('.faq-item', {
        duration: 0.8,
        y: 50,
        opacity: 0,
        stagger: 0.2,
        scrollTrigger: {
            trigger: '.faq-section',
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
        }
    });
}

function initializeHoverEffects() {
    // Contact method hover effects
    const contactMethods = document.querySelectorAll('.contact-method');
    contactMethods.forEach(method => {
        method.addEventListener('mouseenter', function() {
            gsap.to(this, {
                duration: 0.3,
                y: -5,
                boxShadow: '0 10px 25px rgba(0, 0, 0, 0.15)',
                ease: 'power2.out'
            });
        });
        
        method.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.3,
                y: 0,
                boxShadow: '0 5px 15px rgba(0, 0, 0, 0.1)',
                ease: 'power2.out'
            });
        });
    });
    
    // Social icon hover effects
    const socialIcons = document.querySelectorAll('.social-icons a');
    socialIcons.forEach(icon => {
        icon.addEventListener('mouseenter', function() {
            gsap.to(this, {
                duration: 0.3,
                scale: 1.1,
                y: -3,
                ease: 'power2.out'
            });
        });
        
        icon.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.3,
                scale: 1,
                y: 0,
                ease: 'power2.out'
            });
        });
    });
    
    // FAQ item hover effects
    const faqItems = document.querySelectorAll('.faq-item');
    faqItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            gsap.to(this, {
                duration: 0.3,
                y: -5,
                boxShadow: '0 15px 35px rgba(0, 0, 0, 0.15)',
                ease: 'power2.out'
            });
        });
        
        item.addEventListener('mouseleave', function() {
            gsap.to(this, {
                duration: 0.3,
                y: 0,
                boxShadow: '0 5px 20px rgba(0, 0, 0, 0.1)',
                ease: 'power2.out'
            });
        });
    });
}


function navLinksAnimation() {
var navLinks = document.querySelectorAll("nav ul li");

navLinks.forEach((e) => {
    e.addEventListener("mouseenter", function () {
      gsap.to(e, {
        y: -8,
        duration: 0.5,
      });
    });
    e.addEventListener("mouseleave", function () {
      gsap.to(e, {
        y: 0,
        duration: 0.5,
      });
    });
  });
}
navLinksAnimation();    