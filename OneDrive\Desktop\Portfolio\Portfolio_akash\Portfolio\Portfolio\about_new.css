/* ====================================================== Reset & Base Styles ====================================================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ====================================================== Loading Screen ====================================================== */
.loader-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loader {
    text-align: center;
}

.loader-text {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
}

.loader-text span {
    font-size: 3rem;
    font-weight: 700;
    color: white;
    animation: letterBounce 1.5s infinite;
}

.loader-text span:nth-child(2) { animation-delay: 0.1s; }
.loader-text span:nth-child(3) { animation-delay: 0.2s; }
.loader-text span:nth-child(4) { animation-delay: 0.3s; }
.loader-text span:nth-child(5) { animation-delay: 0.4s; }

.loader-progress {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
}

.loader-progress::after {
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    background: white;
    border-radius: 2px;
    animation: progressBar 2s ease-in-out infinite;
}

@keyframes letterBounce {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-20px); }
}

@keyframes progressBar {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* ====================================================== Navigation ====================================================== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    padding: 20px 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav_logo img {
    height: 50px;
    border-radius: 50%;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-links a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: #667eea;
}

.contact_btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white !important;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: transform 0.3s ease;
}

.contact_btn:hover {
    transform: translateY(-2px);
}

/* ====================================================== Hero Section ====================================================== */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    right: 15%;
    animation-delay: 1s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 2s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 30%;
    right: 30%;
    animation-delay: 3s;
}

.shape-5 {
    width: 40px;
    height: 40px;
    top: 10%;
    right: 50%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 40px;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 20px;
}

.title-line {
    display: block;
    font-size: 1.5rem;
    opacity: 0.8;
}

.title-name {
    display: block;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-subtitle {
    display: block;
    font-size: 1.8rem;
    opacity: 0.9;
    color: #ffd700;
}

.hero-description {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 40px;
    line-height: 1.7;
}

.hero-stats {
    display: flex;
    gap: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffd700;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* ====================================================== Profile Section ====================================================== */
.profile-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.profile-image {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
    border: 4px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.8), rgba(118, 75, 162, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.profile-image:hover .profile-overlay {
    opacity: 1;
}

.tech-icons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.tech-icons i {
    font-size: 2rem;
    color: white;
    animation: iconPulse 2s ease-in-out infinite;
}

.tech-icons i:nth-child(2) { animation-delay: 0.5s; }
.tech-icons i:nth-child(3) { animation-delay: 1s; }
.tech-icons i:nth-child(4) { animation-delay: 1.5s; }

@keyframes iconPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.profile-glow {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    opacity: 0.3;
    animation: glow 3s ease-in-out infinite;
    z-index: -1;
}

@keyframes glow {
    0%, 100% { transform: scale(1); opacity: 0.3; }
    50% { transform: scale(1.1); opacity: 0.6; }
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 2;
}

.scroll-text {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 10px;
}

.scroll-arrow {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* ====================================================== Story Section ====================================================== */
.story-section {
    padding: 100px 0;
    background: white;
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 15px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
    opacity: 0.8;
}

.story-timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.story-timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 4px;
    background: linear-gradient(to bottom, #667eea, #764ba2);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: 60px;
    display: flex;
    align-items: center;
}

.timeline-item:nth-child(odd) {
    flex-direction: row;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-item::before {
    content: attr(data-year);
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    z-index: 2;
}

.timeline-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.timeline-icon i {
    font-size: 1.5rem;
    color: white;
}

.timeline-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    width: 45%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.timeline-content:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.timeline-content h3 {
    font-size: 1.4rem;
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

.timeline-content p {
    color: #666;
    line-height: 1.6;
}

/* ====================================================== Skills Showcase ====================================================== */
.skills-showcase {
    padding: 100px 0;
    background: #f8f9fa;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.skill-category {
    background: white;
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.skill-category:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.category-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 25px;
}

.category-icon i {
    font-size: 2rem;
    color: white;
}

.skill-category h3 {
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 20px;
    font-weight: 600;
}

.skill-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.skill-tag {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* ====================================================== Personal Section ====================================================== */
.personal-section {
    padding: 100px 0;
    background: white;
}

.personal-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.personal-text h2 {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 25px;
    font-weight: 700;
}

.personal-text p {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.7;
    margin-bottom: 30px;
}

.personal-highlights {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: background 0.3s ease;
}

.highlight-item:hover {
    background: #e9ecef;
}

.highlight-item i {
    font-size: 1.2rem;
    color: #667eea;
}

.quote-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    position: relative;
    box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
}

.quote-icon {
    font-size: 2rem;
    margin-bottom: 20px;
    opacity: 0.8;
}

.quote-text {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 20px;
    font-style: italic;
}

.quote-author {
    font-weight: 600;
    opacity: 0.9;
}

/* ====================================================== CTA Section ====================================================== */
.cta-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.cta-content p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 40px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.btn-primary, .btn-secondary {
    padding: 15px 30px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* ====================================================== Responsive Design ====================================================== */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 20px;
    }

    .profile-image {
        width: 200px;
        height: 200px;
    }

    .story-timeline::before {
        left: 30px;
    }

    .timeline-item {
        flex-direction: row !important;
        padding-left: 80px;
    }

    .timeline-item::before {
        left: 30px;
        transform: translateX(-50%);
    }

    .timeline-icon {
        left: 30px;
    }

    .timeline-content {
        width: 100%;
    }

    .personal-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .navbar {
        padding: 15px 20px;
    }

    .nav-links {
        gap: 20px;
    }
}
