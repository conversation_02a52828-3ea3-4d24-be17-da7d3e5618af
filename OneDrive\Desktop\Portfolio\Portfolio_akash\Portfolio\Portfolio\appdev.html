<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="appdev.css">
    <link rel="icon" type="image" href="loading(1).png">
    <title>App Developer - Akash</title>
</head>
<body>

    <div class="button-loader">
        <div class="lft-slide"></div>
        <div class="rgt-slide"></div>
    </div>

    <!-- <====================================================== Loader Coded Here ======================================================> -->

    <div class="loader">

    </div>

    <!-- <====================================================== Custom Cursor ======================================================> -->
    <div class="cursor"><i class="fa-solid fa-arrow-right"></i></div>

    <!-- <====================================================== Navbar (Same as designer) ======================================================> -->
    <nav>
        <div class="nav_logo"><a href="index.html"><img src="main_logo(1).jpg" alt="Akash"></a></div>
        <ul>
            <li><a href="project.html">Projects</a></li>
            <li><a href="Akash(Resume).pdf">Resume</a></li>
            <li><a href="hackathon.html">Hackathons</a></li>
        </ul>
        <a href="contact.html" class="contact_btn">Contact Me</a>
    </nav>

    <!-- <====================================================== Hero Section ==============================================> -->
    <section class="hero-section">
        <div class="hero-content">
            <div class="hero-text">
                <h1>App Developer</h1>
                <p class="hero-subtitle">Crafting innovative mobile experiences for Android & iOS</p>
                <p class="hero-description">Passionate about building user-centric mobile applications with modern technologies. Specializing in native Android development, cross-platform solutions, and seamless user experiences.</p>
                <div class="hero-buttons">
                    <a href="contact.html" class="btn-connect">
                        <span class="btn-text">Let's Connect</span>
                        <span class="btn-icon"><i class="fas fa-arrow-right"></i></span>
                    </a>
                </div>
            </div>
            <div class="hero-visual">
                <div class="app-showcase">
                    <div class="showcase-container">
                        <div class="app-image-slider">
                            <div class="app-slide">
                                <img src="fitbit(2).png" alt="Fitness App">
                                <div class="slide-overlay">
                                    <h4>Fitness Tracker</h4>
                                </div>
                            </div>
                            <div class="app-slide">
                                <img src="#" alt="Notes App">
                                <div class="slide-overlay">
                                    <h4>Echo Notes</h4>
                                </div>
                            </div>
                            <div class="app-slide">
                                <img src="Signature.png" alt="Security App">
                                <div class="slide-overlay">
                                    <h4>Signature Forgery Detection</h4>
                                </div>
                            </div>
                            <div class="app-slide">
                                <img src="#" alt="Game App">
                                <div class="slide-overlay">
                                    <h4>Fake news Detection</h4>
                                </div>
                            </div>
                            <div class="app-slide">
                                <img src="#" alt="Adventure App">
                                <div class="slide-overlay">
                                    <h4>DeepFake Audio Detection</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- <====================================================== Skills Section ==============================================> -->
    <section class="skills-section">
        <div class="container">
            <h2>Development Expertise</h2>
            <div class="skills-grid">
                <div class="skill-card">
                    <div class="skill-icon">
                        <i class="fab fa-android"></i>
                    </div>
                    <h3>Android Development</h3>
                    <p>Native Android apps using Kotlin/Java, Android Studio, and modern architecture patterns</p>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">
                        <i class="fab fa-apple"></i>
                    </div>
                    <h3>iOS Development</h3>
                    <p>Swift-based iOS applications with UIKit and SwiftUI frameworks</p>
                </div>
                <div class="skill-card">
                    <div class="skill-icon">
                        <i class="fab fa-react"></i>
                    </div>
                    <h3>Cross-Platform</h3>
                    <p>React Native and Flutter for efficient multi-platform development</p>
                </div>
            </div>
        </div>
    </section>



    <!-- <====================================================== Process Section ==============================================> -->
    <section class="process-section">
        <div class="container">
            <h2>Development Process Flow</h2>
            <div class="process-flowchart">
                <div class="flow-step" data-step="1">
                    <div class="flow-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="flow-content">
                        <h3>Research & Planning</h3>
                        <p>Understanding user needs, market research, and technical planning</p>
                        <div class="flow-details">
                            <span class="detail-tag">User Research</span>
                            <span class="detail-tag">Market Analysis</span>
                            <span class="detail-tag">Tech Stack</span>
                        </div>
                    </div>
                    <div class="flow-arrow">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                </div>

                <div class="flow-step" data-step="2">
                    <div class="flow-icon">
                        <i class="fas fa-pencil-ruler"></i>
                    </div>
                    <div class="flow-content">
                        <h3>Design & Prototyping</h3>
                        <p>Creating wireframes, UI/UX design, and interactive prototypes</p>
                        <div class="flow-details">
                            <span class="detail-tag">Wireframes</span>
                            <span class="detail-tag">UI Design</span>
                            <span class="detail-tag">Prototypes</span>
                        </div>
                    </div>
                    <div class="flow-arrow">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                </div>

                <div class="flow-step" data-step="3">
                    <div class="flow-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="flow-content">
                        <h3>Development</h3>
                        <p>Clean code implementation with best practices and testing</p>
                        <div class="flow-details">
                            <span class="detail-tag">Frontend</span>
                            <span class="detail-tag">Backend</span>
                            <span class="detail-tag">Testing</span>
                        </div>
                    </div>
                    <div class="flow-arrow">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                </div>

                <div class="flow-step" data-step="4">
                    <div class="flow-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <div class="flow-content">
                        <h3>Launch & Support</h3>
                        <p>App store deployment, monitoring, and ongoing maintenance</p>
                        <div class="flow-details">
                            <span class="detail-tag">Deployment</span>
                            <span class="detail-tag">Monitoring</span>
                            <span class="detail-tag">Updates</span>
                        </div>
                    </div>
                </div>

                <!-- Connecting Lines -->
                <div class="flow-line line-1"></div>
                <div class="flow-line line-2"></div>
                <div class="flow-line line-3"></div>
            </div>
        </div>
    </section>







    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js" integrity="sha512-7eHRwcbYkK4d9g/6tD/mhkf++eoTHwpNM9woBxtPUBWm67zeAfFC+HrdoE2GanKeocly/VxeLvIqwvCdk7qScg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js" integrity="sha512-onMTRKJBKz8M1TnqqDuGBlowlH0ohFzMXYRNebz+yOcc5TQr/zAKsthzhuv0hiyUKEiQEQXEynnXCvNTOk50dg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="appdev.js"></script>
</body>
</html>