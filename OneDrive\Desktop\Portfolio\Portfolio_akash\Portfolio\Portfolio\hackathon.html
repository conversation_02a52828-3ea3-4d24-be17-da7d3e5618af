<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="hackathon.css">
    <link rel="icon" type="image" href="loading(1).png">
    <title>My Hackathon</title>
</head>
<body>
        <nav>
        <div class="nav_logo"><a href="index.html"><img src="main_logo(1).jpg" alt="Akash"></a></div>
        <ul>
            <li><a href="project.html">Projects</a></li>
            <li><a href="Akash(Resume).pdf">Resume</a></li>
            <li><a href="https://devfolio.co/@akash26504">Hackathons</a></li>
        </ul>
        <a href="contact.html" class="contact_btn">Contact Me</a>
    </nav>
    </body>
    <style>
   @font-face {
    font-family: "Media";
    src: url('font\ 1.otf');
}
@font-face {
    font-family: "SF Pro";
    src: url('font\ 2.otf');
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
::-webkit-scrollbar{
    display: none; 
}
::selection{
    background-color: #f78c00;
    color: black;
}
nav {
    width: 100%;
    height: 100px;
    position: fixed;
    top: 0px;
    background: linear-gradient(45deg, #0e3771, #ffffff, #8e8e8e);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 100px;
    z-index: 90;
}

.nav_logo {
    width: 12%;
    height: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.nav_logo img {
    width: 100%;
    height: 230%;
    object-fit: cover;
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    flex: 1;
    padding: 0;
}

nav ul li {
    display: inline-block;
    margin: 0 35px;
    justify-content: center;
}

nav ul li a {
    text-decoration: none;
    color: rgb(0, 0, 0);
    font-size: 20px;
    justify-content: center;
    font-weight: 650;
    font-family: "SF Pro";
    transition: all 0.3s ease;
}

nav ul li a:hover, nav ul li a.active {
    color: #000000;
}

a.contact_btn{
    display: inline-block;
    width: 150px;
    height: 50px;
    background: black;
    display: flex;
    font-family: "SF Pro";
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: rgb(255, 255, 255);
    border-radius: 8px;
    transform-origin: center;
    transition: .5s ease;
}
a.contact_btn:hover{
    transform: scaleX(1.1);
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.543);
}
        #bg-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background:linear-gradient(80deg, #0e3771, #ffffff, #8e8e8e);
        }

        .container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 2rem;
        }

        header {
            text-align: center;
            padding: 6rem 0;
            font-family:"SF Pro";
            font-weight: 60;
        }

        header h1 {
            font-size: 3rem;
            color: #2c3e50;
        }

        .timeline {
            position: relative;
            max-width: 1000px;
            margin: 0 auto; 
        }

        .timeline::after {
            content: '';
            position: absolute;
            width: 4px;
            background-color: #002281;
            top: 0;
            bottom: 0;
            left: 50%;
            margin-left: -2px;
        }

        .timeline-container {
            padding: 10px 40px;
            position: relative;
            background-color: inherit;
            width: 50%;
            height: 250px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
        }

        .timeline-container.left {
            left: 0;
        }

        .timeline-container.right {
            left: 50%;
        }

        .timeline-container::after {
            content: '';
            position: absolute;
            width: 25px;
            height: 25px;
            right: -17px;
            background-color: rgb(201, 201, 201);
            border: 4px solid #000000;
            top: 50%;
            transform: translateY(-50%);
            border-radius: 50%;
            z-index: 1;
        }

        .right::after {
            left: -16px;
        }

        .content {
            padding: 20px 30px;
            background-color: rgb(221, 218, 218);
            position: relative;
            border-radius: 6px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            font-family: "SF Pro";
        }

        h2 {
            color: #101010;
            font-family: "SF Pro";
        }

        .team-images {
            display: flex;
            margin-top: 15px;
        }

        .team-member-placeholder {
            width: 40px;
            height: 40px;
            background-color: #e0e0e0;
            border-radius: 50%;
            margin-right: -10px; /* Overlap effect */
        }

        .image-placeholder-opposite {
            width: 180px;
            height: 180px;
            background-color: transparent;
            border-radius: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #000000;
            font-size: 1rem;
            
        }

        .image-group {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 20px;
        }

        .timeline-container.left .image-group {
            left: 100%;
            margin-left: 100px;
        }

        .timeline-container.right .image-group {
            right: 100%;
            margin-right: 100px;
            flex-direction: row-reverse;
        }

        .image-placeholder-extra {
            width: 180px;
            height: 180px;
            background-color: transparent;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #000000;
            font-size: 0.8rem;
            flex-shrink: 0;
        }

        .image-placeholder-opposite,
        .image-placeholder-extra {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }

        .image-placeholder-opposite:hover,
        .image-placeholder-extra:hover {
            transform: translateY(-10px) scale(1.05);
            box-shadow: 0 15px 30px rgba(0,0,0,0.2);
        }

    </style>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;700&display=swap" rel="stylesheet">
</head>
<body>

    <canvas id="bg-canvas"></canvas>

    <div class="container">
        <header>
            <h1>My Hackathon Timeline</h1>
        </header>

        <div class="timeline">
            <div class="timeline-container left">
                <div class="content">
                    <h2>GameForge 1.0[Reva University]  </h2>
                    <p>Built game based on theme lost in time[Winners-Best Gameplay]</p>
                    <div class="team-images">
                        <div class="team-member-placeholder">
                            <img src="akash.jpg.jpg" alt="Gamethon Image" style="width: 100%; height: 130%; border-radius: 8px;" />
                        </div>
                        <div class="team-member-placeholder">
                            <img src="adarsh.jpg" alt="Gamethon Image" style="width: 100%; height: auto; border-radius: 8px;" />
                        </div>
                        <div class="team-member-placeholder">
                            <img src="dinesh.jpg" alt="Gamethon Image" style="width: 80%; height: 130%; border-radius: 8px;" />
                        </div>
                        <div class="team-member-placeholder">
                            <img src="Rishav.jpg" alt="Gamethon Image" style="width: 100%; height: 130%; border-radius: 8px;" />
                        </div>
                        <div class="team-member-placeholder">
                            <img src="Pranky.jpg" alt="Gamethon Image" style="width: 100%; height: 130%; border-radius: 8px;" />
                        </div>
                    </div>
                </div>
                <div class="image-group">
                    <!-- Image for "AI Challenge" - Main -->
                    <div class="image-placeholder-opposite">
                        <img src="Reva(1).jpg" alt="Gamethon Image" style="width: 100%; height: auto; border-radius: 8px;" />
                    </div>
                    <!-- Image for "AI Challenge" - Extra 1 -->
                    <div class="image-placeholder-extra">
                        <img src="Reva(2).jpg" alt="Gamethon Image" style="width: 100%; height: auto; border-radius: 8px;" />
                    </div>
                    <!-- Image for "AI Challenge" - Extra 2 -->
                    <div class="image-placeholder-extra">
                        <img src="Reva(3).jpg" alt="Gamethon Image" style="width: 100%; height: auto; border-radius: 8px;" />
                    </div>
                    <div class="image-placeholder-extra">
                        <img src="Reva(4).jpg" alt="Gamethon Image" style="width: 100%; height: auto; border-radius: 8px;" />
                    </div>
                </div>
            </div>
    <div class="timeline-container right">
    <div class="content">
        <h2>Gamethon 7.0 [Jyothi Institute of Technology]</h2>
        <p>Developed a game - Defend our fort, using Unreal Engine.</p>
        <div class="team-images">
            <div class="team-member-placeholder">
                <img src="akash.jpg.jpg" alt="Gamethon Image" style="width: 100%; height: 130%; border-radius: 8px;" />
            </div>
            <div class="team-member-placeholder">
                <img src="adarsh.jpg" alt="Gamethon Image" style="width: 100%; height: 130%; border-radius: 8px;" />
            </div>
            <div class="team-member-placeholder">
                <img src="saiyam.jpg" alt="Gamethon Image" style="width: 100%; height: 130%; border-radius: 8px;" />
            </div>
        </div>
    </div>
    <div class="image-group">
        <div class="image-placeholder-opposite">
            <!-- Image for "Gamethon 7.0" - Main -->
            <img src="Gamethon7(3).jpg" alt="Gamethon Image" style="width: 100%; height: auto; border-radius: 8px;" />
        </div>
        <!-- Image for "Gamethon 7.0" - Extra 1 -->
        <div class="image-placeholder-extra">
            <img src="Gamethon7(4).jpg" alt="Gamethon Image" style="width: 100%; height: auto; border-radius: 8px;" />  
        </div>
        <!-- Image for "Gamethon 7.0" - Extra 2 -->
        <div class="image-placeholder-extra">
            <img src="Gamethon7(1).jpg" alt="Gamethon Image" style="width: 100%; height: auto; border-radius: 8px;" />
        </div>
    </div>
</div>
            <div class="timeline-container left">
                <div class="content">
                    <h2>DSATM 8hr Hackathon</h2>
                    <p>Created website that finds best hackathons globally.</p>
                </div>
                <div class="image-group">
                    <!-- Image for "GreenTech Hackathon" - Main -->
                    <div class="image-placeholder-opposite">
                        <img src="Hackathon.jpg" alt="Gamethon Image" style="width: 100%; height: auto; border-radius: 8px;" />
                    </div>
                    <!-- Image for "GreenTech Hackathon" - Extra 1 -->
                    <div class="image-placeholder-extra">
                        <img src="HackathonHunt.png" alt="Gamethon Image" style="width: 100%; height: auto; border-radius: 8px;" /> 
            </div>
        </div>
    </div>
     <script src="https://cdn.jsdelivr.net/npm/locomotive-scroll@beta/bundled/locomotive-scroll.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"
        integrity="sha512-7eHRwcbYkK4d9g/6tD/mhkf++eoTHwpNM9woBxtPUBWm67zeAfFC+HrdoE2GanKeocly/VxeLvIqwvCdk7qScg=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js" integrity="sha512-7eHRwcbYkK4d9g/6tD/mhkf++eoTHwpNM9woBxtPUBWm67zeAfFC+HrdoE2GanKeocly/VxeLvIqwvCdk7qScg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js" integrity="sha512-onMTRKJBKz8M1TnqqDuGBlowlH0ohFzMXYRNebz+yOcc5TQr/zAKsthzhuv0hiyUKEiQEQXEynnXCvNTOk50dg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="hackathon.js"></script>
    <script>
        
        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({
            canvas: document.getElementById('bg-canvas'),
            alpha: true
        });
        renderer.setSize(window.innerWidth, window.innerHeight);

        const gridHelper = new THREE.GridHelper(200, 51);
        scene.add(gridHelper);

        camera.position.y = 5;
        camera.position.z = 20;
        camera.rotation.x = -0.2;

        function animate() {
            requestAnimationFrame(animate);
            gridHelper.position.z += 0.02;
            if (gridHelper.position.z > 4) {
                gridHelper.position.z = 0;
            }
            renderer.render(scene, camera);
        }
        animate();

        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });


    </script>

</body>
</html>