@font-face {
    font-family: "Media";
    src: url('font\ 1.otf');
}
@font-face {
    font-family: "SF Pro";
    src: url('font\ 2.otf');
}



/* ====================================================== Reset & Base Styles ====================================================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ====================================================== Loading Screen ====================================================== */
.loader-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #000000 0%, #141217 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease;
}

.loader {
    text-align: center;
}

.loader-text {
    display: flex;
    gap: 10px;
    margin-bottom: 30px;
}

.loader-text span {
    font-size: 3rem;
    font-weight: 700;
    color: white;
    animation: letterBounce 1.5s infinite;
}

.loader-text span:nth-child(2) { animation-delay: 0.1s; }
.loader-text span:nth-child(3) { animation-delay: 0.2s; }
.loader-text span:nth-child(4) { animation-delay: 0.3s; }
.loader-text span:nth-child(5) { animation-delay: 0.4s; }

.loader-progress {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    overflow: hidden;
}

.loader-progress::after {
    content: '';
    display: block;
    width: 100%;
    height: 100%;
    background: white;
    border-radius: 2px;
    animation: progressBar 2s ease-in-out infinite;
}

@keyframes letterBounce {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-20px); }
}

@keyframes progressBar {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* ====================================================== Navigation ====================================================== */
nav {
    width: 100%;
    height: 100px;
    position: fixed;
    top: 0px;
    background: linear-gradient(45deg, #0e3771, #838383, #8e8e8e);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 100px;
    z-index: 90;
}

.nav_logo {
    width: 12%;
    height: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.nav_logo img {
    width: 100%;
    height: 230%;
    object-fit: cover;
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    flex: 1;
    padding: 0;
}

nav ul li {
    display: inline-block;
    margin: 0 35px;
    justify-content: center;
}

nav ul li a {
    text-decoration: none;
    color: rgb(0, 0, 0);
    font-size: 20px;
    justify-content: center;
    font-weight: 650;
    font-family: "SF Pro";
    transition: all 0.3s ease;
}

nav ul li a:hover, nav ul li a.active {
    color: #000000;
}

a.contact_btn{
    display: inline-block;
    width: 150px;
    height: 50px;
    background: black;
    display: flex;
    font-family: "SF Pro";
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: rgb(255, 255, 255);
    border-radius: 8px;
    transform-origin: center;
    transition: .5s ease;
}
a.contact_btn:hover{
    transform: scaleX(1.1);
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.543);
}


/* ====================================================== Hero Section ====================================================== */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background:linear-gradient(85deg, #0e3771, #838383, #8e8e8e);
    background-size: 100% 100%, 600px 600px, 800px 800px, 400px 400px;
    background-attachment: fixed;
    color: white;
    overflow: hidden;
    animation: gradientShift 20s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%, 0% 0%, 100% 100%, 50% 50%;
    }
    25% {
        background-position: 25% 75%, 25% 25%, 75% 75%, 25% 75%;
    }
    50% {
        background-position: 50% 100%, 50% 50%, 50% 50%, 100% 0%;
    }
    75% {
        background-position: 75% 25%, 75% 75%, 25% 25%, 75% 25%;
    }
}




.hero-content {
    position: relative;
    z-index: 3;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 60px 50px;
    background: rgba(255, 255, 255, 0.08);
    -webkit-backdrop-filter: blur(15px);
    backdrop-filter: blur(15px);
    border-radius: 25px;
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4);
    min-height: 500px;
}

.hero-title {
    font-size: 4rem;
    font-weight: 700;
    font-family: "Media";
    letter-spacing: 4px;
    line-height: 1.2;
    margin-bottom: 20px;
}

.title-line {
    display: block;
    font-size: 1.5rem;
    opacity: 0.8;
}

.title-name {
    display: block;
    background: linear-gradient(45deg, #fff, #f0f0f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.title-subtitle {
    display: block;
    font-size: 1.8rem;
    opacity: 0.9;
    color: #ffd700;
}

.hero-description {
    font-size: 1.2rem;
    opacity: 0.9;
    font-family: "SF Pro";
    margin-bottom: 40px;
    line-height: 1.7;
}

.hero-stats {
    display: flex;
    gap: 40px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    color: #ffd700;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* ====================================================== Profile Section ====================================================== */
.profile-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.profile-image {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    overflow: hidden;
    position: relative;
    border: 4px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, rgba(30, 60, 114, 0.9), rgba(42, 82, 152, 0.9));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.4s ease;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
}

.profile-image:hover .profile-overlay {
    opacity: 1;
}

.profile-icons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    align-items: center;
    justify-items: center;
}

.profile-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
    transform: scale(0);
    animation: iconPop 0.6s ease forwards;
}

.profile-icon:nth-child(1) { animation-delay: 0.1s; }
.profile-icon:nth-child(2) { animation-delay: 0.2s; }
.profile-icon:nth-child(3) { animation-delay: 0.3s; }
.profile-icon:nth-child(4) { animation-delay: 0.4s; }

.profile-icon:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.6);
    transform: scale(1.1);
}

.profile-icon i {
    font-size: 1.2rem;
    color: white;
    transition: transform 0.3s ease;
}

.profile-icon:hover i {
    transform: scale(1.2);
}

@keyframes iconPop {
    0% {
        transform: scale(0) rotate(-180deg);
        opacity: 0;
    }
    70% {
        transform: scale(1.1) rotate(10deg);
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}





.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 2;
}

.scroll-text {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-bottom: 10px;
}

.scroll-arrow {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* ====================================================== Section Header Styles ====================================================== */
.section-header {
    text-align: center;
    margin-bottom: 80px;
}



.section-title {
    font-size: 3rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 15px;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
    opacity: 0.8;
}











/* ====================================================== Personal Section ====================================================== */
.personal-section {
    padding: 0 0 100px 0;
    background: linear-gradient(100deg, #0e3771, #838383, #8e8e8e);
}

.personal-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
}

.personal-text h2 {
    font-size: 2.5rem;
    color: #000000;
    margin-bottom: 25px;
    font-weight: 700;
}

.personal-text p {
    font-size: 1.1rem;
    color: #000000;
    line-height: 1.7;
    margin-bottom: 30px;
}

.personal-highlights {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    transition: background 0.3s ease;
}

.highlight-item:hover {
    background: #e9ecef;
}

.highlight-item i {
    font-size: 1.2rem;
    color: #667eea;
}

.quote-card {
    background: linear-gradient(135deg, #667eea, #1023a0);
    color: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    position: relative;
    box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
}

.quote-icon {
    font-size: 2rem;
    margin-bottom: 20px;
    opacity: 0.8;
}

.quote-text {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: 20px;
    font-style: italic;
}

.quote-author {
    font-weight: 600;
    opacity: 0.9;
}

/* ====================================================== Footer ====================================================== */
.footer {
    background: #333;
    color: white;
    padding: 20px 0;
    text-align: center;
    font-size: 0.9rem;
}

.footer p {
    margin: 0;
    opacity: 0.8;
}

/* ====================================================== Responsive Design ====================================================== */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 20px;
    }

    .profile-image {
        width: 200px;
        height: 200px;
    }



    .story-timeline::before {
        left: 30px;
    }

    .timeline-item {
        flex-direction: row !important;
        padding-left: 80px;
    }

    .timeline-item::before {
        left: 30px;
        transform: translateX(-50%);
    }

    .timeline-icon {
        left: 30px;
    }

    .timeline-content {
        width: 100%;
    }

}

    .personal-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .navbar {
        padding: 15px 20px;
    }

    .nav-links {
        gap: 20px;
    }

