<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Akash - Creative Developer</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="stylesheet" href="about.css">
    <link rel="icon" type="image" href="loading(1).png">
</head>
<body>
    <!-- Loading Screen -->
    <div class="loader-wrapper">
        <div class="loader">
            <div class="loader-text">
                <span>A</span><span>K</span><span>A</span><span>S</span><span>H</span>
            </div>
            <div class="loader-progress"></div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav_logo">
            <a href="index.html">
                <img src="main_logo(1).jpg" alt="Akash">
            </a>
        </div>
        <ul class="nav-links">
            <li><a href="project.html">Projects</a></li>
            <li><a href="Akash(Resume).pdf">Resume</a></li>
            <li><a href="hackathon.html">Hackathons</a></li>
        </ul>
        <a href="contact.html" class="contact_btn">Contact Me</a>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="hero-background">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
                <div class="shape shape-5"></div>
            </div>
        </div>
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title">
                    <span class="title-line">Hi, I'm</span>
                    <span class="title-name">Akash K.</span>
                    <span class="title-subtitle">Creative Developer</span>
                </h1>
                <p class="hero-description">
                    Passionate tech enthusiast crafting digital experiences through code, creativity, and innovation.
                </p>
                <div class="hero-stats">
                    <div class="stat-item">
                        <span class="stat-number">2+</span>
                        <span class="stat-label">Years Experience</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">15+</span>
                        <span class="stat-label">Projects Built</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">5+</span>
                        <span class="stat-label">Technologies</span>
                    </div>
                </div>
            </div>
            <div class="hero-visual">
                <div class="profile-container">
                    <div class="profile-image">
                        <img src="akash.jpg" alt="Akash Profile">
                        <div class="profile-overlay">
                            <div class="tech-icons">
                                <i class="fab fa-android"></i>
                                <i class="fab fa-react"></i>
                                <i class="fab fa-python"></i>
                                <i class="fas fa-brain"></i>
                            </div>
                        </div>
                    </div>
                    <div class="profile-glow"></div>
                </div>
            </div>
        </div>
        <div class="scroll-indicator">
            <div class="scroll-text">Scroll to explore</div>
            <div class="scroll-arrow">
                <i class="fas fa-chevron-down"></i>
            </div>
        </div>
    </section>

    <!-- Story Section -->
    <section class="story-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">My Journey</h2>
                <p class="section-subtitle">From curiosity to creation</p>
            </div>
            <div class="story-timeline">
                <div class="timeline-item" data-year="2022">
                    <div class="timeline-icon">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <div class="timeline-content">
                        <h3>The Beginning</h3>
                        <p>Started my engineering journey with a spark of curiosity for technology and problem-solving.</p>
                    </div>
                </div>
                <div class="timeline-item" data-year="2023">
                    <div class="timeline-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <div class="timeline-content">
                        <h3>First Lines of Code</h3>
                        <p>Dove into web development and Android app development, building my first projects with passion.</p>
                    </div>
                </div>
                <div class="timeline-item" data-year="2024">
                    <div class="timeline-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="timeline-content">
                        <h3>Leadership & Growth</h3>
                        <p>Led technical events at college tech club "Vinyasa" and expanded into AI and Machine Learning.</p>
                    </div>
                </div>
                <div class="timeline-item" data-year="Now">
                    <div class="timeline-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="timeline-content">
                        <h3>Creating Impact</h3>
                        <p>Building meaningful tech solutions and contributing to impactful projects while continuously learning.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Showcase -->
    <section class="skills-showcase">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">What I Do</h2>
                <p class="section-subtitle">Turning ideas into reality</p>
            </div>
            <div class="skills-grid">
                <div class="skill-category">
                    <div class="category-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h3>Development</h3>
                    <div class="skill-tags">
                        <span class="skill-tag">Web Development</span>
                        <span class="skill-tag">Android Apps</span>
                        <span class="skill-tag">React</span>
                        <span class="skill-tag">Kotlin</span>
                        <span class="skill-tag">Python</span>
                    </div>
                </div>
                <div class="skill-category">
                    <div class="category-icon">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3>AI & ML</h3>
                    <div class="skill-tags">
                        <span class="skill-tag">Machine Learning</span>
                        <span class="skill-tag">Image Processing</span>
                        <span class="skill-tag">Flask APIs</span>
                        <span class="skill-tag">Data Analysis</span>
                    </div>
                </div>
                <div class="skill-category">
                    <div class="category-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3>Leadership</h3>
                    <div class="skill-tags">
                        <span class="skill-tag">Event Management</span>
                        <span class="skill-tag">Team Leading</span>
                        <span class="skill-tag">Tech Club</span>
                        <span class="skill-tag">Hackathons</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Personal Touch -->
    <section class="personal-section">
        <div class="container">
            <div class="personal-content">
                <div class="personal-text">
                    <h2>Beyond the Code</h2>
                    <p>When I'm not coding, you'll find me exploring new technologies, organizing tech events, or working on creative projects that blend storytelling with technology. I believe in the power of continuous learning and the magic that happens when creativity meets technical expertise.</p>
                    <div class="personal-highlights">
                        <div class="highlight-item">
                            <i class="fas fa-trophy"></i>
                            <span>Tech Club Leader</span>
                        </div>
                        <div class="highlight-item">
                            <i class="fas fa-gamepad"></i>
                            <span>Game Developer</span>
                        </div>
                        <div class="highlight-item">
                            <i class="fas fa-lightbulb"></i>
                            <span>Innovation Enthusiast</span>
                        </div>
                    </div>
                </div>
                <div class="personal-visual">
                    <div class="quote-card">
                        <div class="quote-icon">
                            <i class="fas fa-quote-left"></i>
                        </div>
                        <p class="quote-text">"What keeps me going is a constant desire to learn, build, and create meaningful tech solutions."</p>
                        <div class="quote-author">- Akash K.</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact CTA -->
    <section class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2>Let's Create Something Amazing Together</h2>
                <p>Ready to bring your ideas to life? Let's connect and build the future, one line of code at a time.</p>
                <div class="cta-buttons">
                    <a href="contact.html" class="btn-primary">Get In Touch</a>
                    <a href="project.html" class="btn-secondary">View My Work</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>
    <script src="about.js"></script>
</body>
</html>
