# <PERSON><PERSON><PERSON>'s Portfolio Website

A modern, responsive portfolio website showcasing <PERSON><PERSON><PERSON>'s work as an App Developer and UI/UX Designer.

## 🚀 Features

### About Page
- **Hero Section**: Beautiful introduction with animated profile image and gradient text
- **Story Section**: Personal journey and background information
- **Skills & Expertise**: Categorized skills with interactive hover effects
- **Image Gallery**: Animated placeholders with hover overlays
- **Values Section**: Core principles and what drives the work
- **Call-to-Action**: Engaging section to encourage collaboration

### Animations & Interactions
- **GSAP Animations**: Smooth scroll-triggered animations
- **Custom Cursor**: Interactive cursor that responds to hoverable elements
- **Hover Effects**: Rich hover animations on cards, buttons, and gallery items
- **Parallax Effects**: Subtle parallax scrolling for depth
- **Loading Screen**: Elegant loading animation with logo

### Design System
- **Modern Gradient**: Consistent blue-to-gray gradient theme
- **Typography**: SF Pro font family for clean, professional look
- **Responsive Design**: Fully responsive across all device sizes
- **Accessibility**: Proper contrast ratios and keyboard navigation

## 📁 File Structure

```
Portfolio/
├── index.html          # Main homepage
├── about.html          # About page (NEW)
├── project.html        # Projects showcase
├── contact.html        # Contact form
├── appdev.html         # App development page
├── designer.html       # Design work page
├── hackathon.html      # Hackathon projects
├── style.css           # Main stylesheet
├── about.css           # About page styles (NEW)
├── about.js            # About page interactions (NEW)
├── project.css         # Project page styles
├── contact.css         # Contact page styles
├── designer.css        # Designer page styles
├── appdev.css          # App dev page styles
├── mediaquery.css      # Responsive design rules
├── script.js           # Main JavaScript
├── project.js          # Project page interactions
├── contact.js          # Contact form functionality
├── designer.js         # Designer page interactions
└── assets/             # Images, fonts, and media files
```

## 🎨 About Page Sections

### 1. Hero Section
- Animated title with gradient text effect
- Profile image with 3D perspective and hover effects
- Personal introduction and location

### 2. Story Section
- Personal journey and background
- Motivation and approach to work
- Professional philosophy

### 3. Skills & Expertise
- **App Development**: React Native, Flutter, iOS, Android
- **Design**: UI/UX, Figma, Adobe Creative Suite, Prototyping
- **Web Technologies**: HTML/CSS/JS, React.js, Node.js, MongoDB

### 4. Image Gallery
- 6 animated placeholders with icons
- Hover overlays with descriptions
- Categories: Development, Design, Hackathons, Teamwork, Learning, Projects

### 5. Values Section
- **Innovation**: Creative problem-solving
- **Passion**: Dedication to quality
- **User-Centric**: Focus on user experience
- **Adaptability**: Quick learning and adaptation

### 6. Call-to-Action
- Encourages collaboration
- Links to contact and projects
- Gradient background with animated buttons

## 🛠️ Technologies Used

- **HTML5**: Semantic markup
- **CSS3**: Modern styling with gradients, animations, and flexbox/grid
- **JavaScript**: Interactive functionality and animations
- **GSAP**: Professional animation library
- **Font Awesome**: Icon library
- **Responsive Design**: Mobile-first approach

## 🎯 Key Features

### Animations
- Fade-in animations on scroll
- Staggered animations for lists and grids
- Hover effects with smooth transitions
- Parallax scrolling effects
- Typing effect for hero title

### Interactive Elements
- Custom cursor that scales on hover
- Gallery items with overlay information
- Skill tags with floating animations
- Value cards with rotating icons
- Smooth button hover effects

### Performance
- Optimized animations with GSAP
- Debounced scroll events
- Efficient CSS animations
- Lazy loading for images

## 📱 Responsive Design

The about page is fully responsive with breakpoints at:
- **Desktop**: 1200px+
- **Tablet**: 768px - 1199px
- **Mobile**: 480px - 767px
- **Small Mobile**: < 480px

## 🚀 Getting Started

1. Open `index.html` in a web browser
2. Navigate to the "About" page using the navigation menu
3. Explore the interactive elements and animations
4. Test responsiveness on different screen sizes

## 🎨 Customization

### Colors
The main color scheme uses:
- Primary Blue: `#0e3771`
- Secondary Gray: `#8e8e8e`
- Dark Text: `#191919`
- Light Background: `#f8f9fa`

### Content
- Update personal information in `about.html`
- Replace placeholder images with actual photos
- Modify skills and values to match your profile
- Customize the story section with your journey

### Animations
- Adjust animation timing in `about.js`
- Modify GSAP ScrollTrigger settings
- Customize hover effects in `about.css`

## 📞 Contact

For questions or collaboration opportunities, visit the contact page or reach out through the provided links.

---

**Built with ❤️ by Akash** 