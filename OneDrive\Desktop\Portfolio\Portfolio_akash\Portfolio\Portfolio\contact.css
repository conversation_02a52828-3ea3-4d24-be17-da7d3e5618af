/* ===== CONTACT PAGE STYLES ===== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

nav {
    width: 100%;
    height: 100px;
    position: fixed;
    top: 0px;
    background: linear-gradient(45deg, #0e3771, #ffffff, #8e8e8e);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 100px;
    z-index: 90;
}

.nav_logo {
    width: 12%;
    height: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.nav_logo img {
    width: 100%;
    height: 230%;
    object-fit: cover;
}

nav ul {
    list-style: none;
}

nav ul li {
    display: inline-block;
    margin: 0 35px;
}

nav ul li a {
    text-decoration: none;
    color: rgb(0, 0, 0);
    font-size: 20px;
    font-weight: 650;
    transition: all 0.3s ease;
}

nav ul li a:hover, nav ul li a.active {
    color: #000000;
}

a.contact_btn{
    display: inline-block;
    width: 150px;
    height: 50px;
    background: black;
;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: rgb(255, 255, 255);
    border-radius: 8px;
    transform-origin: center;
    transition: .5s ease;
}
a.contact_btn:hover{
    transform: scaleX(1.1);
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.543);
}

/* Contact Hero Section */
.contact-hero {
    padding: 120px 0 60px;
    background: linear-gradient(94deg, #0e3771, #ffffff, #8e8e8e);
    color: rgb(0, 0, 0);
    text-align: center;
}

.contact-hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    font-weight: 700;
}

.contact-hero p {
    font-size: 1.3rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* Contact Content */
.contact-content {
    padding: 80px 0;
    background: linear-gradient(94deg, #0e3771, #ffffff, #8e8e8e);
}

.contact-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

/* Contact Form Section */
.contact-form-section h2 {
    font-size: 2rem;
    margin-bottom: 2rem;
    color: #000000;
}

.contact-form {
    background: #92c4c9;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.8rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.submit-btn {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: rgb(0, 0, 0);
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0 auto;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 123, 255, 0.3);
}

/* Contact Info Section */
.contact-info-section h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #000000;
}

.contact-info-section > p {
    font-size: 1.1rem;
    color: #000000;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.contact-methods {
    margin-bottom: 3rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #5393ba;
    border-radius: 10px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-method:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.contact-icon {
    background: linear-gradient(135deg, #0e3771, #2a5298);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(14, 55, 113, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.contact-details h3 {
    font-size: 1.1rem;
    margin-bottom: 0.3rem;
    color: #000000;
}

.contact-details p {
    color: #000000;
    margin: 0;
}

/* Social Links */
.social-links {
    margin-top: 2rem;
}

.social-links h3 {
    font-size: 1.3rem;
    margin-bottom: 1.5rem;
    color: #333;
    text-align: left;
}

.social-icons {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: 1.2rem;
    flex-wrap: wrap;
}

.social-icons a {
    background: linear-gradient(135deg, #0e3771, #2a5298);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(14, 55, 113, 0.3);
    flex-shrink: 0;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.social-icons a:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 8px 25px rgba(14, 55, 113, 0.4);
    background: linear-gradient(135deg, #2a5298, #4facfe);
    border-color: rgba(255, 255, 255, 0.4);
}

/* Individual social icon colors for brand recognition */
.social-icons a[aria-label="Behance"]:hover {
    background: linear-gradient(135deg, #1769ff, #0057ff);
}

.social-icons a[aria-label="LinkedIn"]:hover {
    background: linear-gradient(135deg, #0077b5, #005885);
}

.social-icons a[aria-label="GitHub"]:hover {
    background: linear-gradient(135deg, #333, #24292e);
}

.social-icons a[aria-label="Devfolio"]:hover {
    background: linear-gradient(135deg, #3770ff, #2563eb);
}


/* Responsive Design */
@media (max-width: 768px) {
    .contact-hero h1 {
        font-size: 2.5rem;
    }

    .contact-hero p {
        font-size: 1.1rem;
    }

    .contact-wrapper {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-form {
        padding: 1.5rem;
    }

    .social-icons {
        justify-content: center;
    }

    .faq-grid {
        grid-template-columns: 1fr;
    }

    nav ul {
        display: none;
    }
}

@media (max-width: 480px) {
    .contact-hero {
        padding: 100px 0 40px;
    }

    .contact-hero h1 {
        font-size: 2rem;
    }

    .contact-content {
        padding: 60px 0;
    }

    .contact-form {
        padding: 1rem;
    }

    .contact-method {
        flex-direction: column;
        text-align: center;
    }
}