@font-face {
    font-family: "Media";
    src: url('font\ 1.otf');
}
@font-face {
    font-family: "SF Pro";
    src: url('font\ 2.otf');
}   

/* ====================================================== Reset & Base Styles ====================================================== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #b19696;
    background: linear-gradient(135deg, #000931 0%, #764ba2 100%);
    min-height: 100vh;
}



::-webkit-scrollbar {
    display: none;
}

::selection {
    background-color: #667eea;
    color: white;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* ====================================================== Loader Styles ====================================================== */
.button-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    display: flex;
    pointer-events: none;
}

.lft-slide, .rgt-slide {
    width: 50%;
    height: 100%;
    background: #1a1a2e;
}

.lft-slide {
    position: absolute;
    left: -100%;
}

.rgt-slide {
    position: absolute;
    right: -100%;
}

.loader {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10000;
}

/* ====================================================== Custom Cursor ====================================================== */
.cursor {
    width: 70px;
    height: 70px;
    background-color: white;
    border: 4px solid #191919;
    border-radius: 50px;
    position: fixed;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.29);
    display: flex;
    align-items: center;
    justify-content: center;
    transform: translate(-50%, -50%);
    z-index: 999;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease, transform 0.1s ease;
    will-change: transform;
}

.cursor i {
    font-size: 25px;
    transform: rotate(-45deg);
    pointer-events: none;
    color: #191919;
}




/* <====================================================== Loader Coded Here ======================================================> */


/* ====================================================== Navigation Styles ====================================================== */
nav {
    width: 100%;
    height: 100px;
    position: fixed;
    top: 0px;
    background: linear-gradient(45deg, #0e3771, #ffffff, #8e8e8e);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 100px;
    z-index: 90;
    font-family: "SF Pro";
}

.nav_logo {
    width: 12%;
    height: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.nav_logo img {
    width: 100%;
    height: 230%;
    object-fit: cover;
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    flex: 1;
    padding: 0;
}

nav ul li {
    display: inline-block;
    margin: 0 35px;
    justify-content: center;
}

nav ul li a {
    text-decoration: none;
    color: rgb(0, 0, 0);
    font-size: 20px;
    justify-content: center;
    font-weight: 650;
    font-family: "SF Pro";
    transition: all 0.3s ease;
}

nav ul li a:hover, nav ul li a.active {
    color: #000000;
}

a.contact_btn{
    display: inline-block;
    width: 150px;
    height: 50px;
    background: black;
    display: flex;
    font-family: "SF Pro";
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: rgb(255, 255, 255);
    border-radius: 8px;
    transform-origin: center;
    transition: .5s ease;
}
a.contact_btn:hover{
    transform: scaleX(1.1);
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.543);
}


/* ====================================================== Hero Section ====================================================== */
.hero-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 100px 40px 60px;
    background: linear-gradient(90deg, #0e3771, #ffffff, #8e8e8e);
    color: rgb(47, 37, 37);
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-text h1 {
    font-size: 3.5rem;
    font-weight: 700;
    font-family: "Media";
    letter-spacing: 2px;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #000000, #004078);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.4rem;
    margin-bottom: 20px;
    opacity: 0.9;
}

.hero-description {
    font-size: 1.1rem;
    font-family: "SF Pro";
    margin-bottom: 40px;
    opacity: 0.8;
    line-height: 1.7;
}

.hero-buttons {
    display: flex;
    gap: 20px;
}

.btn-connect {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 15px;
    padding: 18px 35px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.25));
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50px;
    color: white;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    overflow: hidden;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.btn-connect::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.btn-connect:hover::before {
    left: 100%;
}

.btn-connect:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
    border-color: rgba(255, 255, 255, 0.6);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.35));
}

.btn-text {
    position: relative;
    z-index: 2;
}

.btn-icon {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.btn-connect:hover .btn-icon {
    background: rgba(255, 255, 255, 0.3);
    transform: translateX(5px);
}

.btn-icon i {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
}

.btn-connect:hover .btn-icon i {
    transform: rotate(-45deg);
}

/* ====================================================== App Showcase ====================================================== */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.app-showcase {
    width: 100%;
    max-width: 600px;
    height: 500px;
    position: relative;
    overflow: hidden;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.showcase-container {
    width: 100%;
    height: 100%;
    position: relative;
}

.app-image-slider {
    display: flex;
    width: 500%;
    height: 100%;
    animation: slideShow 15s infinite;
}

.app-slide {
    width: 20%;
    height: 100%;
    position: relative;
    flex-shrink: 0;
}

.app-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 20px;
}

.slide-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 30px 20px 20px;
    border-radius: 0 0 20px 20px;
}

.slide-overlay h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

@keyframes slideShow {
    0%, 18% { transform: translateX(0%); }
    20%, 38% { transform: translateX(-20%); }
    40%, 58% { transform: translateX(-40%); }
    60%, 78% { transform: translateX(-60%); }
    80%, 98% { transform: translateX(-80%); }
    100% { transform: translateX(0%); }
}

/* ====================================================== Skills Section ====================================================== */
.skills-section {
    padding: 100px 0;
    background: linear-gradient(98deg, #0e3771, #ffffff, #8e8e8e);
}

.skills-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
    color: #333;
}

.skills-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 40px;
    max-width: 900px;
    margin: 0 auto;
}

.skill-card {
    background: white;
    padding: 40px 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.skill-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.skill-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.skill-icon i {
    font-size: 2rem;
    color: white;
}

.skill-card h3 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: #333;
}

.skill-card p {
    color: #666;
    line-height: 1.6;
}


/* ====================================================== Process Section ====================================================== */
.process-section {
    padding: 100px 0;
    background: linear-gradient(96deg, #0e3771, #ffffff, #8e8e8e);
    position: relative;
    overflow: visible;
    min-height: 600px;
    display: block;
    visibility: visible;
    border: 3px solid #667eea;
    margin: 20px 0;
}

.process-section h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 60px;
    color: #333;
    position: relative;
    z-index: 10;
    display: block;
    visibility: visible;
    opacity: 1;
}

.process-flowchart {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 10;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    align-items: center;
    padding: 20px;
    visibility: visible;
}

.flow-step {
    background: white;
    border-radius: 20px;
    padding: 30px 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    position: relative;
    transition: all 0.3s ease;
    border: 2px solid #e9ecef;
    text-align: center;
    height: 350px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    z-index: 5;
    visibility: visible;
    opacity: 1;
}

.flow-step:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border-color: #667eea;
}

.flow-step[data-step="1"] .flow-icon {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
}

.flow-step[data-step="2"] .flow-icon {
    background: linear-gradient(135deg, #4ecdc4, #44a08d);
}

.flow-step[data-step="3"] .flow-icon {
    background: linear-gradient(135deg, #45b7d1, #96c93d);
}

.flow-step[data-step="4"] .flow-icon {
    background: linear-gradient(135deg, #f093fb, #f5576c);
}

.flow-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    position: relative;
    z-index: 3;
}

.flow-icon i {
    font-size: 1.8rem;
    color: white;
}

.flow-content {
    text-align: center;
    flex: 1;
}

.flow-content h3 {
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: #333;
    font-weight: 600;
}

.flow-content p {
    color: #555;
    line-height: 1.6;
    margin-bottom: 20px;
    font-size: 1rem;
}

.flow-details {
    display: flex;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
}

.detail-tag {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.75rem;
    font-weight: 500;
    opacity: 0.9;
}

.flow-arrow {
    position: absolute;
    right: -15px;
    top: 50%;
    transform: translateY(-50%);
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 4;
}

.flow-arrow i {
    color: white;
    font-size: 1rem;
    transform: rotate(90deg);
}

.flow-step:last-child .flow-arrow {
    display: none;
}

/* Horizontal Connecting Lines */
.flow-line {
    position: absolute;
    top: 50%;
    height: 4px;
    background: linear-gradient(to right, #667eea, #764ba2);
    transform: translateY(-50%);
    border-radius: 2px;
    opacity: 0.6;
    z-index: 1;
}

.line-1 {
    left: 25%;
    width: 25%;
}

.line-2 {
    left: 50%;
    width: 25%;
}

.line-3 {
    left: 75%;
    width: 25%;
}

/* ====================================================== Responsive Design ====================================================== */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-text h1 {
        font-size: 2.5rem;
    }

    .app-showcase {
        max-width: 450px;
        height: 350px;
    }

    .slide-overlay h4 {
        font-size: 1rem;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }

    .skills-grid {
        max-width: none;
    }

    .process-flowchart {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .flow-step {
        padding: 30px 20px;
        height: auto;
        min-height: 280px;
        margin-bottom: 20px;
    }

    .flow-icon {
        width: 60px;
        height: 60px;
    }

    .flow-icon i {
        font-size: 1.5rem;
    }

    .flow-content h3 {
        font-size: 1.2rem;
    }

    .flow-content p {
        font-size: 0.85rem;
    }

    .flow-details {
        gap: 6px;
    }

    .detail-tag {
        font-size: 0.7rem;
        padding: 4px 8px;
    }

    .flow-line {
        display: none;
    }

    .flow-arrow {
        right: 50%;
        bottom: -15px;
        top: auto;
        transform: translateX(50%);
        width: 25px;
        height: 25px;
    }

    .flow-arrow i {
        font-size: 0.9rem;
        transform: rotate(0deg);
    }

    nav {
        padding: 15px 20px;
    }

    nav ul {
        gap: 20px;
    }

    .hero-section {
        padding: 80px 20px 40px;
    }

    .process-section {
        padding: 60px 0;
    }

    .process-section h2 {
        font-size: 2rem;
        margin-bottom: 40px;
    }
}

/* Tablet responsive */
@media (max-width: 991px) and (min-width: 769px) {
    .process-flowchart {
        grid-template-columns: repeat(2, 1fr);
        gap: 25px;
    }

    .flow-step {
        height: 300px;
    }

    .app-showcase {
        max-width: 500px;
        height: 400px;
    }
}

/* Small tablet responsive */
@media (max-width: 991px) {
    .process-flowchart {
        max-width: 800px;
    }

    .flow-step {
        border: 2px solid #dee2e6;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .flow-step:hover {
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
    }
}

