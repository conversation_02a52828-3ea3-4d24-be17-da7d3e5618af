@font-face {
    font-family: "Media";
    src: url('font\ 1.otf');
}
@font-face {
    font-family: "SF Pro";
    src: url('font\ 2.otf');
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

::-webkit-scrollbar {
    display: none;
}

::selection {
    background-color: #f78c00;
    color: black;
}

body {
    background: linear-gradient(95deg, #0e3771, #ffffff, #8e8e8e);
    color: white;
    font-family: "SF Pro", sans-serif;
}


/* Button Loader */
.button-loader {
    width: 100%;
    height: 100vh;
    background-color: transparent;
    position: fixed;
    z-index: 999;
    pointer-events: none;
    display: none;
}

.lft-slide {
    width: 50%;
    height: 100%;
    background-color: #191919;
    position: absolute;
    left: -100%;
}

.rgt-slide {
    width: 50%;
    height: 100%;
    background-color: #191919;
    position: absolute;
    right: -100%;
}

/* Navigation */
nav {
    width: 100%;
    height: 100px;
    position: fixed;
    top: 0px;
    background: linear-gradient(45deg, #0e3771, #ffffff, #8e8e8e);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 100px;
    z-index: 90;
}

.nav_logo {
    width: 12%;
    height: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.nav_logo img {
    width: 100%;
    height: 230%;
    object-fit: cover;
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    flex: 1;
    padding: 0;
}

nav ul li {
    display: inline-block;
    margin: 0 35px;
    justify-content: center;
}

nav ul li a {
    text-decoration: none;
    color: rgb(0, 0, 0);
    font-size: 20px;
    justify-content: center;
    font-weight: 650;
    transition: all 0.3s ease;
}

nav ul li a:hover, nav ul li a.active {
    color: #000000;
}

a.contact_btn{
    display: inline-block;
    width: 150px;
    height: 50px;
    background: black;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: rgb(255, 255, 255);
    border-radius: 8px;
    transform-origin: center;
    transition: .5s ease;
}
a.contact_btn:hover{
    transform: scaleX(1.1);
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.543);
}

/* Projects Hero Section */
.projects-hero {
    width: 100%;
    height: 40vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 100px;
    padding: 0 20px;
    text-align: center;
}

.projects-hero h1 {
    font-family: "Media", sans-serif;
    font-size: 70px;
    letter-spacing: 3px;
    margin-bottom: 20px;
    background: black;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.projects-hero p {
    font-size: 22px;
    max-width: 700px;
    color: #2b2b2b;
}

/* Filter Section */
.filter-container {
    width: 100%;
    display: flex;
    justify-content: center;
    margin: 20px 0 40px;
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
}

.filter-btn {
    padding: 10px 20px;
    background-color: #2a2a2a;
    border: none;
    border-radius: 30px;
    color: white;
    font-family: "SF Pro", sans-serif;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover, .filter-btn.active {
    background-color: #f78c00;
    color: black;
}

/* Projects Grid */
.projects-grid {
    width: 90%;
    max-width: 1400px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    padding-bottom: 100px;
}

.project-card {
    background-color: #222;
    border-radius: 15px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.project-img {
    width: 100%;
    height: 250px;
    position: relative;
    overflow: hidden;
}

.project-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.project-card:hover .project-img img {
    transform: scale(1.1);
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .overlay {
    opacity: 1;
}

.project-links {
    display: flex;
    gap: 20px;
}

.project-links a {
    width: 50px;
    height: 50px;
    background-color: #f78c00;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: black;
    font-size: 20px;
    transition: all 0.3s ease;
}

.project-links a:hover {
    transform: scale(1.1);
    background-color: white;
}

.project-info {
    padding: 20px;
}

.project-info h3 {
    font-size: 22px;
    margin-bottom: 10px;
    color: #f78c00;
}

.project-info p {
    font-size: 16px;
    color: #ccc;
    margin-bottom: 15px;
}

.project-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.project-tags span {
    padding: 5px 12px;
    background-color: #333;
    border-radius: 20px;
    font-size: 14px;
    color: #ddd;
}

/* Responsive Design */
@media screen and (max-width: 1024px) {
    nav {
        padding: 0 50px;
    }

    .nav_logo {
        width: 15%;
    }

    nav ul li {
        margin: 0 20px;
    }

    nav ul li a {
        font-size: 18px;
    }

    .projects-hero h1 {
        font-size: 3rem;
    }

    .projects-hero p {
        font-size: 1.1rem;
    }

    .filter-buttons {
        gap: 12px;
    }

    .filter-btn {
        padding: 8px 16px;
        font-size: 15px;
    }

    .projects-grid {
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 25px;
    }
}

@media screen and (max-width: 768px) {
    nav {
        padding: 0 30px;
        height: 80px;
        flex-wrap: wrap;
    }

    .nav_logo {
        width: 20%;
    }

    nav ul {
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }

    nav ul li {
        margin: 0 8px;
    }

    nav ul li a {
        font-size: 16px;
    }

    nav a.contact_btn {
        width: 120px;
        height: 40px;
        font-size: 0.8rem;
    }

    .projects-hero {
        padding: 100px 20px 40px;
    }

    .projects-hero h1 {
        font-size: 2.5rem;
    }

    .projects-hero p {
        font-size: 1rem;
    }

    .filter-container {
        padding: 0 20px;
    }

    .filter-buttons {
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }

    .filter-btn {
        padding: 8px 15px;
        font-size: 14px;
    }

    .projects-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
        padding: 0 20px;
    }
}

@media screen and (max-width: 480px) {
    nav {
        padding: 0 15px;
        height: 70px;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .nav_logo {
        width: 30%;
        margin-bottom: 5px;
    }

    nav ul {
        gap: 8px;
        margin: 5px 0;
    }

    nav ul li a {
        font-size: 14px;
    }

    nav a.contact_btn {
        width: 100px;
        height: 35px;
        font-size: 0.7rem;
        margin-top: 5px;
    }

    .projects-hero {
        padding: 80px 15px 30px;
    }

    .projects-hero h1 {
        font-size: 2rem;
    }

    .projects-hero p {
        font-size: 0.9rem;
    }

    .filter-container {
        padding: 0 15px;
    }

    .filter-buttons {
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }

    .filter-btn {
        padding: 8px 20px;
        font-size: 13px;
        width: 200px;
    }

    .projects-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 0 15px;
    }

    .project-card {
        max-width: 100%;
    }
}